# Test Python Script for Scheduled Tasks
import sys
import json
import base64
import time
from datetime import datetime

def main(config):
    """
    Test script for scheduled task execution
    """
    print("=== 定时任务测试脚本 ===")
    print(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"接收到的配置: {json.dumps(config, indent=2, ensure_ascii=False)}")
    
    # 模拟一些工作
    print("\n开始执行任务...")
    for i in range(3):
        print(f"处理步骤 {i+1}/3...")
        time.sleep(1)
    
    print("\n任务执行完成!")
    print("输出一些测试数据:")
    print("- 处理文件数量: 150")
    print("- 成功处理: 148")
    print("- 失败处理: 2")
    print("- 总耗时: 3.2秒")
    
    if config.get('enable_logging'):
        print("\n日志记录已启用")
    
    print("\n=== 脚本执行结束 ===")

if __name__ == "__main__":
    # The Base64 encoded config is passed as the first and only argument
    if len(sys.argv) > 1:
        try:
            config_base64 = sys.argv[1]
            config_json_bytes = base64.b64decode(config_base64)
            config_json = config_json_bytes.decode('utf-8')
            config_data = json.loads(config_json)
            main(config_data)
        except Exception as e:
            print(f"Error decoding or parsing config from Base64: {e}", file=sys.stderr)
            sys.exit(1)
    else:
        # Fallback for direct execution without arguments, for testing
        print("Running with default empty config (no arguments provided).")
        main({})
