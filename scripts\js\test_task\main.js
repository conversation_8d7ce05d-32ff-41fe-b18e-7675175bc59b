// Test JS Script for Scheduled Tasks
const fs = require('fs');
const path = require('path');

// Get configuration from command line arguments
let config = {};
if (process.argv[2]) {
  try {
    const configString = Buffer.from(process.argv[2], 'base64').toString('utf-8');
    config = JSON.parse(configString);
  } catch (e) {
    console.error('Failed to parse configuration:', e.message);
    process.exit(1);
  }
}

console.log('=== 定时任务测试脚本 (JS) ===');
console.log(`执行时间: ${new Date().toLocaleString('zh-CN')}`);
console.log('接收到的配置:');
console.log(JSON.stringify(config, null, 2));

console.log('\n开始执行任务...');

// 模拟异步工作
function simulateWork(step, total) {
  return new Promise(resolve => {
    setTimeout(() => {
      console.log(`处理步骤 ${step}/${total}...`);
      resolve();
    }, 1000);
  });
}

async function main() {
  try {
    for (let i = 1; i <= 3; i++) {
      await simulateWork(i, 3);
    }
    
    console.log('\n任务执行完成!');
    console.log('输出一些测试数据:');
    console.log('- 处理文件数量: 200');
    console.log('- 成功处理: 195');
    console.log('- 失败处理: 5');
    console.log('- 总耗时: 3.1秒');
    
    if (config.enable_logging) {
      console.log('\n日志记录已启用');
    }
    
    console.log('\n=== 脚本执行结束 ===');
  } catch (error) {
    console.error('脚本执行出错:', error.message);
    process.exit(1);
  }
}

main();
