# 输入框失去焦点问题修复

## 问题描述
在定时任务管理页面中，当删除列表中的任务后，所有输入框会失去焦点，用户无法继续输入。这个问题影响了用户体验，特别是在批量操作时。

## 问题原因
1. **Vue组件重新渲染**: 删除任务后调用`loadTasks()`重新加载数据，导致整个组件树重新渲染
2. **DOM元素被替换**: 重新渲染时，所有输入框的DOM元素被销毁并重新创建，失去了焦点状态
3. **缺少唯一标识**: 输入框没有稳定的key属性，Vue无法正确复用DOM元素

## 解决方案

### 1. 添加输入框key属性
为所有输入框添加唯一的key属性，帮助Vue正确识别和复用DOM元素：

```vue
<!-- 搜索输入框 -->
<input :key="`search-${inputKey}`" v-model="searchQuery" />

<!-- 任务名称输入框 -->
<input :key="`task-name-${inputKey}`" v-model="currentTask.name" />

<!-- HTTP配置输入框 -->
<input :key="`http-url-${inputKey}`" v-model="currentTask.httpConfig.url" />
<textarea :key="`http-headers-${inputKey}`" v-model="currentTask.httpConfig.headers" />
<textarea :key="`http-body-${inputKey}`" v-model="currentTask.httpConfig.body" />

<!-- 时间和Cron表达式输入框 -->
<input :key="`execute-time-${inputKey}`" v-model="currentTask.executeTime" />
<input :key="`cron-expression-${inputKey}`" v-model="currentTask.cronExpression" />
```

### 2. 优化删除操作
避免不必要的重新加载，直接操作本地数据：

```javascript
// 原来的做法（会导致重新渲染）
await loadTasks() // 重新加载任务列表

// 新的做法（直接操作本地数组）
const taskIndex = tasks.value.findIndex(t => t.id === task.id)
if (taskIndex > -1) {
  tasks.value.splice(taskIndex, 1)
}

// 更新输入框key确保DOM正确更新
await nextTick(() => {
  inputKey.value = Date.now()
})
```

### 3. 添加响应式inputKey
创建一个响应式的inputKey变量，用于强制更新DOM元素：

```javascript
// 输入框key，用于解决删除后失去焦点的问题
const inputKey = ref(Date.now())
```

### 4. 优化任务列表渲染
为任务列表项和复选框添加稳定的key：

```vue
<!-- 任务列表项 -->
<div v-for="task in filteredTasks" :key="`task-${task.id}-${inputKey}`">

<!-- 任务复选框 -->
<input type="checkbox" :key="`task-checkbox-${task.id}-${inputKey}`" />

<!-- 全选复选框 -->
<input type="checkbox" :key="`select-all-${inputKey}`" />
```

## 修复的功能

### 单个任务删除
- ✅ 删除任务后输入框保持焦点
- ✅ 避免不必要的页面重新渲染
- ✅ 保持用户的输入状态

### 批量任务删除
- ✅ 批量删除后所有输入框保持可用状态
- ✅ 选中状态正确清理
- ✅ 性能优化，避免重复加载

### 输入框状态保持
- ✅ 搜索框保持焦点和内容
- ✅ 模态框中的输入框不受影响
- ✅ 复选框状态正确维护

## 技术实现细节

### Vue Key属性的作用
- 帮助Vue识别哪些元素需要重新创建，哪些可以复用
- 当key值改变时，强制重新创建元素
- 当key值相同时，尽可能复用现有元素

### nextTick的使用
- 确保DOM更新完成后再执行后续操作
- 避免在DOM更新过程中出现状态不一致

### 本地数据操作 vs 重新加载
- 本地操作：直接修改响应式数组，Vue自动更新视图
- 重新加载：从服务器获取最新数据，但会导致组件重新渲染

## 测试验证

### 测试步骤
1. 打开定时任务管理页面
2. 在搜索框中输入内容并保持焦点
3. 选择一个或多个任务进行删除
4. 验证搜索框是否仍然保持焦点
5. 验证其他输入框是否正常工作

### 预期结果
- ✅ 删除任务后搜索框保持焦点
- ✅ 可以继续在搜索框中输入
- ✅ 模态框中的输入框正常工作
- ✅ 复选框状态正确更新

## 注意事项

1. **inputKey的更新时机**: 只在删除操作后更新，避免频繁更新影响性能
2. **key属性的唯一性**: 确保每个输入框的key都是唯一的
3. **异步操作处理**: 使用nextTick确保DOM更新完成

这个修复确保了用户在删除任务后能够继续正常使用所有输入框，大大改善了用户体验。
