import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'

const api = {
  getCmdScripts: () => ipcRenderer.invoke('db:get-cmd-scripts'),
  addCmdScript: (data) => ipcRenderer.invoke('db:add-cmd-script', data),
  updateCmdScript: (data) => ipcRenderer.invoke('db:update-cmd-script', data),
  deleteCmdScript: (id) => ipcRenderer.invoke('db:delete-cmd-script', id),
  runCmdScript: (script) => ipcRenderer.invoke('cmd:run-script', script),
  getScriptLogs: (scriptId) => ipcRenderer.invoke('db:get-script-logs', scriptId),

  // Python Scripts
  getPythonScripts: () => ipcRenderer.invoke('fs:get-python-scripts'),
  getPythonScriptConfig: (scriptName) => ipcRenderer.invoke('db:get-python-script-config', scriptName),
  updatePythonScriptConfig: (config) => ipcRenderer.invoke('db:update-python-script-config', config),
  addPythonScript: (name) => ipcRenderer.invoke('fs:add-python-script', name),
  deletePythonScript: (name) => ipcRenderer.invoke('fs:delete-python-script', name),
  runPythonScript: (args) => ipcRenderer.invoke('python:run-script', args),
  testPythonEnvironment: () => ipcRenderer.invoke('test:python-environment'),

  // JS Scripts
  getJsScripts: () => ipcRenderer.invoke('fs:get-js-scripts'),
  getJsScriptConfig: (scriptName) => ipcRenderer.invoke('db:get-js-script-config', scriptName),
  updateJsScriptConfig: (config) => ipcRenderer.invoke('db:update-js-script-config', config),
  addJsScript: (name) => ipcRenderer.invoke('fs:add-js-script', name),
  deleteJsScript: (name) => ipcRenderer.invoke('fs:delete-js-script', name),
  runJsScript: (args) => ipcRenderer.invoke('js:run-script', args),
  testNodeEnvironment: () => ipcRenderer.invoke('test:node-environment'),

  // Scheduled Tasks CRUD
  getScheduledTasks: () => ipcRenderer.invoke('scheduled-tasks:get-all'),
  createScheduledTask: (taskData) => ipcRenderer.invoke('scheduled-tasks:create', taskData),
  updateScheduledTask: (id, taskData) => ipcRenderer.invoke('scheduled-tasks:update', id, taskData),
  deleteScheduledTask: (id) => ipcRenderer.invoke('scheduled-tasks:delete', id),
  toggleScheduledTaskEnabled: (id, enabled) => ipcRenderer.invoke('scheduled-tasks:toggle-enabled', id, enabled),
  updateScheduledTaskStatus: (id, status, lastRun) => ipcRenderer.invoke('scheduled-tasks:update-status', id, status, lastRun),

  // Scheduled Task Logs
  addScheduledTaskLog: (logData) => ipcRenderer.invoke('scheduled-tasks:add-log', logData),
  getScheduledTaskLogs: (taskId, options) => ipcRenderer.invoke('scheduled-tasks:get-logs', taskId, options),

  // HTTP Request Execution
  executeHttpRequest: (httpConfig) => ipcRenderer.invoke('http:execute-request', httpConfig)
}

if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
  } catch (error) {
    console.error(error)
  }
} else {
  window.electron = electronAPI
  window.api = api
}
